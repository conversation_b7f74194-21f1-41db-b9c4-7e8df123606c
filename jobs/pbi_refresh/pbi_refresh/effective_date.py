import logging
from datetime import date

from pbi_refresh.config import Config
from pbi_refresh.connectors.dataset_manager import RefreshType
from pbi_refresh.connectors.pipeline_manager import trigger_reasons
from pbi_refresh.connectors.report_service import Report, get_reports_by_ids

log = logging.getLogger(__name__)

allowed_events = ["REPORTS_CREATED"]

# TODO: reverse logic here and use SUPPORTED_SOURCES instead. It is better to
# accidentally skip refreshing something by not adding it to the list, rather than
# perform costly refreshes for something we do not need.
IGNORED_SOURCES = (
    "microsoft_daily_active_users",
    "nintendo_cumulative_wishlist_sales",
    "nintendo_discounts",
    "steam_daily_active_users",
    "steam_discounts",
    "steam_wishlist_balance",
)


def get_refresh_type(job_guid: str, config: Config) -> RefreshType | None:
    reasons = trigger_reasons(job_guid, config)
    list_of_events = sorted(
        list(set(reason["event_name"] for reason in reasons["data"]))
    )
    if not list_of_events:
        log.warning(
            "Incremental Refresh has not been fired because no Events have been detected"
        )
        return RefreshType.FULL

    is_only_allowed_events = all(map(lambda e: e in allowed_events, list_of_events))

    if not is_only_allowed_events:
        log.info(
            "Incremental Refresh has not been fired because not allowed Events have been detected: %s",
            list(event for event in list_of_events if event not in allowed_events),
        )
        return RefreshType.FULL
    log.info(
        f"""Incremental Refresh tries to be fired - only allowed Events have been detected:
            {list_of_events}"""
    )

    report_ids, all_have_report_id = find_all_report_ids(reasons)
    if not all_have_report_id:
        log.warning(
            "Incremental Refresh has not been fired because no report_ids have been passed"
        )
        return RefreshType.FULL

    reports = get_reports_by_ids(report_ids, config)

    if all_reports_are_ignored(reports):
        log.info("All received reports are ignored")
        return None

    earliest_date = find_earliest_date_from(reports)
    if earliest_date:
        return (
            RefreshType.INCREMENTAL
            if allow_ir_based_on_earliest_date(earliest_date)
            else RefreshType.FULL
        )
    else:
        return RefreshType.FULL


def all_reports_are_ignored(reports: list[Report]) -> bool:
    return all(map(lambda r: r.source in IGNORED_SOURCES, reports))


def find_all_report_ids(reasons) -> tuple[list[int], bool]:
    all_have_report_id = True
    all_report_ids = []
    for event in reasons["data"]:
        if "report_ids" not in event["event_params"]:
            all_have_report_id = False
            continue
        if len(event["event_params"]["report_ids"]) == 0:
            all_have_report_id = False

        all_report_ids.extend(event["event_params"]["report_ids"])

    return all_report_ids, all_have_report_id


def find_earliest_date_from(reports: list[Report]) -> date | None:
    if any(map(lambda r: r.date_from is None, reports)):
        log.info(
            f"""Incremental Refresh has not been fired because reports do not have valid date_from field:
            {[report.id for report in reports if report.date_from is None]}"""
        )
        return None
    valid_reports = list(filter(lambda r: r.source not in IGNORED_SOURCES, reports))
    return min(
        map(lambda r: r.date_from, valid_reports)
    )  # date_from cannot be None here


def allow_ir_based_on_earliest_date(
    earliest_date: date, current_date: date | None = None
) -> bool:
    if current_date is None:
        current_date = date.today()
    if _earliest_date_after_start_of_previous_quarter(earliest_date, current_date):
        log.info(
            f"Incremental Refresh has been fired because {earliest_date} is after start of previous quarter"
        )
        return True
    else:
        log.info(
            f"Incremental Refresh has not been fired because {earliest_date} is earlier than start of previous quarter"
        )
        return False


def _earliest_date_after_start_of_previous_quarter(
    earliest_date: date, current_date: date
) -> bool:
    quarter = (current_date.month - 1) // 3 + 1
    if quarter == 1:
        quarter = 4
        year = current_date.year - 1
    else:
        quarter -= 1
        year = current_date.year
    return earliest_date >= date(year, 3 * quarter - 2, 1)
